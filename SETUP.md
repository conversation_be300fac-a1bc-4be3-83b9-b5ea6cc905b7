# دليل إعداد وتشغيل نظام إدارة الأعمال Elite

## نظرة عامة
نظام إدارة الأعمال Elite هو تطبيق ويب شامل مصمم خصيصاً للشركات السعودية، يدعم اللغة العربية والمحاسبة المتوافقة مع النظام السعودي.

## المتطلبات الأساسية

### البرامج المطلوبة
- **Node.js** (الإصدار 18 أو أحدث)
- **npm** أو **yarn** أو **pnpm**
- **Git** (اختياري)
- متصفح ويب حديث

### الحسابات المطلوبة
- حساب [Supabase](https://supabase.com) (مجاني)

## خطوات الإعداد

### 1. تحضير المشروع
```bash
# إذا كان لديك Git
git clone [repository-url]
cd elite-business-management

# أو قم بتحميل الملفات مباشرة
```

### 2. تثبيت المتطلبات
```bash
# باستخدام npm
npm install

# أو باستخدام yarn
yarn install

# أو باستخدام pnpm
pnpm install
```

### 3. إعداد قاعدة البيانات (Supabase)

#### أ. إنشاء مشروع Supabase
1. اذهب إلى [supabase.com](https://supabase.com)
2. قم بإنشاء حساب جديد أو تسجيل الدخول
3. اضغط على "New Project"
4. اختر اسماً للمشروع (مثل: elite-business)
5. اختر كلمة مرور قوية لقاعدة البيانات
6. اختر المنطقة الأقرب لك

#### ب. إعداد قاعدة البيانات
1. في لوحة تحكم Supabase، اذهب إلى "SQL Editor"
2. انسخ محتوى ملف `database/schema.sql`
3. الصق المحتوى في SQL Editor
4. اضغط "Run" لتنفيذ الاستعلامات

#### ج. الحصول على مفاتيح API
1. اذهب إلى "Settings" > "API"
2. انسخ "Project URL"
3. انسخ "anon public" key

### 4. إعداد متغيرات البيئة
```bash
# انسخ ملف المثال
cp .env.local.example .env.local

# قم بتعديل الملف وإضافة بياناتك
```

في ملف `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 5. تشغيل التطبيق
```bash
# تشغيل في وضع التطوير
npm run dev

# أو
yarn dev

# أو
pnpm dev
```

### 6. الوصول للتطبيق
افتح المتصفح واذهب إلى: [http://localhost:3000](http://localhost:3000)

## إعداد المصادقة والأدوار

### 1. إنشاء المستخدمين
في لوحة تحكم Supabase:
1. اذهب إلى "Authentication" > "Users"
2. اضغط "Add user"
3. أدخل البريد الإلكتروني وكلمة المرور

### 2. إعداد الأدوار
```sql
-- في SQL Editor، قم بتشغيل:
INSERT INTO user_profiles (id, name, email, role) VALUES
('user-id-from-auth', 'اسم المستخدم', '<EMAIL>', 'admin');
```

## الميزات المتاحة

### ✅ المكتملة
- **الصفحة الرئيسية**: لوحة تحكم شاملة
- **إدارة العملاء**: إضافة، تعديل، حذف، بحث
- **إدارة الموردين**: إدارة شاملة للموردين
- **مندوبو المبيعات**: إدارة فريق المبيعات
- **المحاسبة السعودية**: حساب ضريبة القيمة المضافة (15%)
- **التقارير**: تقارير المبيعات والأداء
- **الفواتير**: إدارة الفواتير والمبيعات

### 🔄 قيد التطوير
- **استيراد Excel**: استيراد العملاء من ملفات Excel
- **تصدير التقارير**: تصدير التقارير بصيغ مختلفة
- **نظام الإشعارات**: إشعارات للمستخدمين
- **نظام المصادقة**: تسجيل الدخول والأدوار

### 📋 مخطط لها
- **إدارة المنتجات**: إضافة وإدارة المنتجات/الخدمات
- **نظام المخزون**: تتبع المخزون والكميات
- **تطبيق الجوال**: تطبيق للهواتف الذكية
- **تكامل الدفع**: ربط مع أنظمة الدفع السعودية

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
Error: Invalid API key
```
**الحل**: تأكد من صحة مفاتيح Supabase في ملف `.env.local`

#### خطأ في تثبيت المتطلبات
```
npm ERR! code EACCES
```
**الحل**: 
```bash
# امسح مجلد node_modules وأعد التثبيت
rm -rf node_modules
npm install
```

#### مشكلة في عرض الخطوط العربية
**الحل**: تأكد من أن المتصفح يدعم الخطوط العربية أو قم بتثبيت خطوط إضافية

### طلب المساعدة
إذا واجهت مشاكل:
1. تحقق من ملف `README.md`
2. راجع رسائل الخطأ في وحدة تحكم المتصفح
3. تأكد من صحة إعدادات قاعدة البيانات

## الأمان والخصوصية

### أفضل الممارسات
- **لا تشارك مفاتيح API**: احتفظ بملف `.env.local` سرياً
- **استخدم كلمات مرور قوية**: للمستخدمين وقاعدة البيانات
- **حدّث التطبيق بانتظام**: للحصول على آخر التحديثات الأمنية

### النسخ الاحتياطي
- قم بعمل نسخة احتياطية من قاعدة البيانات بانتظام
- احتفظ بنسخة من ملفات التطبيق

## التطوير والتخصيص

### هيكل المشروع
```
src/
├── app/                 # صفحات التطبيق
├── components/          # المكونات المشتركة
├── lib/                # المكتبات والدوال المساعدة
└── styles/             # ملفات التصميم

database/
└── schema.sql          # هيكل قاعدة البيانات
```

### إضافة ميزات جديدة
1. أنشئ صفحة جديدة في `src/app/`
2. أضف المكونات المطلوبة في `src/components/`
3. حدّث قاعدة البيانات إذا لزم الأمر

## الدعم والمساهمة

### الحصول على الدعم
- راجع الوثائق في `README.md`
- تحقق من الأسئلة الشائعة
- تواصل مع فريق التطوير

### المساهمة في التطوير
- اقترح ميزات جديدة
- أبلغ عن الأخطاء
- ساهم في تحسين الكود

---

**نظام إدارة الأعمال Elite** - حلول متكاملة لإدارة أعمالك بكفاءة واحترافية 🚀
