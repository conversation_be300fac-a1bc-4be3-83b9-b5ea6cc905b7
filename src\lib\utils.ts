import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// دوال مساعدة للتاريخ
export function formatDate(date: string | Date, locale: string = 'ar-SA'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString(locale)
}

// دوال مساعدة للأرقام
export function formatCurrency(amount: number, currency: string = 'SAR'): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 2
  }).format(amount)
}

export function formatNumber(number: number): string {
  return new Intl.NumberFormat('ar-SA').format(number)
}

// دوال مساعدة للنصوص
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

// دوال مساعدة للتحقق من صحة البيانات
export function isValidSaudiMobile(mobile: string): boolean {
  const saudiMobileRegex = /^05\d{8}$/
  return saudiMobileRegex.test(mobile)
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// دوال مساعدة للمحاسبة
export function calculateVAT(amount: number, rate: number = 0.15): number {
  return Math.round(amount * rate * 100) / 100
}

export function calculateTotal(subtotal: number, vatRate: number = 0.15): { vat: number; total: number } {
  const vat = calculateVAT(subtotal, vatRate)
  const total = subtotal + vat
  return { vat, total }
}

// دوال مساعدة للحالات
export function getStatusColor(status: string): string {
  const statusColors: Record<string, string> = {
    active: 'bg-green-100 text-green-800',
    inactive: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-blue-100 text-blue-800',
    rejected: 'bg-red-100 text-red-800',
    paid: 'bg-green-100 text-green-800',
    unpaid: 'bg-red-100 text-red-800',
    overdue: 'bg-red-100 text-red-800',
    draft: 'bg-gray-100 text-gray-800',
    sent: 'bg-blue-100 text-blue-800'
  }
  
  return statusColors[status] || 'bg-gray-100 text-gray-800'
}

// دوال مساعدة للملفات
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// دوال مساعدة للبحث والفلترة
export function searchInObject(obj: any, searchTerm: string): boolean {
  const searchLower = searchTerm.toLowerCase()
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const value = obj[key]
      if (typeof value === 'string' && value.toLowerCase().includes(searchLower)) {
        return true
      }
      if (typeof value === 'number' && value.toString().includes(searchTerm)) {
        return true
      }
    }
  }
  
  return false
}

// دوال مساعدة للتصدير
export function downloadCSV(data: any[], filename: string): void {
  if (!data.length) return
  
  const headers = Object.keys(data[0])
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
  ].join('\n')
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `${filename}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }
}

// دوال مساعدة للإشعارات
export function showNotification(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info'): void {
  // يمكن تطوير هذه الدالة لاحقاً لإظهار إشعارات جميلة
  console.log(`[${type.toUpperCase()}] ${message}`)
}
